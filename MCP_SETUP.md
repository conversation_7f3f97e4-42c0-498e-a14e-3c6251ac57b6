# BEN2 MCP Server Setup Guide

This guide explains how to use the BEN2 application as an MCP (Model Context Protocol) server, allowing LLMs to use BEN2's background removal capabilities as tools.

## What is MCP?

MCP (Model Context Protocol) is a standardized way to expose tools so that they can be used by LLMs. With MCP support, your BEN2 application can be called as a tool by LLM applications like <PERSON>, Cursor, Cline, and others.

## Features

The BEN2 MCP server exposes two main tools:

1. **Image Background Removal** (`fn`): Remove background from a single image
2. **Video Background Removal** (`process_video`): Remove background from video files

## Setup Instructions

### 1. Install Dependencies

Make sure you have the MCP-enabled version of Gradio installed:

```bash
pip install "gradio[mcp]"
```

### 2. Start the Application

Run the BEN2 application with MCP server enabled:

```bash
python app.py
```

You should see output similar to:
```
Using device: cuda
* Running on local URL:  http://127.0.0.1:7860
* To create a public link, set `share=True` in `launch()`.

🔨 Launching MCP server:
** Streamable HTTP URL: http://127.0.0.1:7860/gradio_api/mcp/
* [Deprecated] SSE URL: http://127.0.0.1:7860/gradio_api/mcp/sse
```

### 3. Configure Your MCP Client

#### For SSE-Compatible Clients (Cursor, Windsurf, Cline)

Add this configuration to your MCP client settings:

```json
{
  "mcpServers": {
    "ben2": {
      "url": "http://127.0.0.1:7860/gradio_api/mcp/sse"
    }
  }
}
```

#### For Claude Desktop

Claude Desktop doesn't support SSE out of the box, so you need to use `mcp-remote`:

1. First, install Node.js if you haven't already
2. Add this configuration to your Claude Desktop MCP settings:

```json
{
  "mcpServers": {
    "ben2": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://127.0.0.1:7860/gradio_api/mcp/sse"
      ]
    }
  }
}
```

## Available Tools

### 1. Image Background Removal (`fn`)

**Description**: Remove background from an image using BEN2 model.

**Input**: 
- `image`: Input image file to remove background from (accepts HTTP/HTTPS URLs)

**Output**: 
- Processed image with background removed
- File path to download the result

### 2. Video Background Removal (`process_video`)

**Description**: Remove background from a video using BEN2 model.

**Input**: 
- `video_path`: Path to the input video file (accepts HTTP/HTTPS URLs)

**Output**: 
- Path to the processed video with background removed

## Usage Examples

Once configured, you can ask your LLM to:

- "Remove the background from this image: [image URL]"
- "Process this video to remove the background: [video URL]"
- "Can you help me remove the background from my photo?"

The LLM will automatically use the BEN2 tools to process your requests.

## Testing the Setup

You can test if the MCP server is working correctly by running:

```bash
python test_mcp.py
```

This will verify that:
- The MCP schema endpoint is accessible
- Both tools are properly exposed
- The server is responding correctly

## Troubleshooting

### Common Issues

1. **Port Already in Use**: If port 7860 is already in use, Gradio will automatically use the next available port. Check the console output for the actual URL.

2. **CUDA Not Available**: The application will automatically fall back to CPU if CUDA is not available, but processing will be slower.

3. **File Upload Issues**: When using the MCP tools, make sure to provide full HTTP/HTTPS URLs for files, as local file paths may not work correctly with all MCP clients.

### Checking Server Status

You can verify the MCP server is running by visiting:
- Main application: `http://127.0.0.1:7860`
- MCP schema: `http://127.0.0.1:7860/gradio_api/mcp/schema`

## Notes

- The MCP server runs alongside the regular Gradio web interface
- Both the web UI and MCP tools use the same underlying BEN2 model
- For best performance, ensure you have a CUDA-compatible GPU
- Video processing is limited to the first 100 frames for performance reasons

## Support

If you encounter any issues with the MCP setup, please check:
1. That all dependencies are properly installed
2. That the application starts without errors
3. That your MCP client configuration is correct
4. That you're using the correct URLs for file inputs
