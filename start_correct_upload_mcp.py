#!/usr/bin/env python3
"""
启动正确配置的upload-mcp服务器
"""

import subprocess
import os
import time
import requests

def 停止现有的upload_mcp进程():
    """停止现有的upload-mcp进程"""
    
    print("🛑 正在停止现有的upload-mcp进程...")
    
    try:
        # 在Windows上查找并停止uvx进程
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"],
            capture_output=True,
            text=True
        )
        
        if "python.exe" in result.stdout:
            print("⚠️ 发现Python进程，可能需要手动停止upload-mcp服务器")
        
        # 尝试使用taskkill停止相关进程
        subprocess.run(
            ["taskkill", "/F", "/IM", "uvx.exe"],
            capture_output=True
        )
        
        print("✅ 已尝试停止现有进程")
        
    except Exception as e:
        print(f"⚠️ 停止进程时出错: {e}")

def 启动正确的upload_mcp服务器():
    """启动正确配置的upload-mcp服务器"""
    
    print("🚀 正在启动正确配置的upload-mcp服务器...")
    
    # 创建上传目录
    upload_dir = os.path.join(os.getcwd(), "mcp_uploads")
    os.makedirs(upload_dir, exist_ok=True)
    print(f"📁 上传目录: {upload_dir}")
    
    # 使用正确的基础URL（不包含/gradio_api/mcp/sse）
    base_url = "http://127.0.0.1:7860"
    
    # 构建命令
    cmd = [
        "uvx",
        "--from", "gradio[mcp]",
        "gradio",
        "upload-mcp",
        base_url,  # 使用基础URL
        upload_dir
    ]
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"✅ upload-mcp服务器已启动，PID: {process.pid}")
        
        # 等待服务器启动
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ upload-mcp服务器运行正常")
            return process, upload_dir
        else:
            stdout, stderr = process.communicate()
            print(f"❌ upload-mcp服务器启动失败")
            print(f"📄 stdout: {stdout}")
            print(f"📄 stderr: {stderr}")
            return None, None
            
    except Exception as e:
        print(f"❌ 启动upload-mcp服务器时出错: {e}")
        return None, None

def 测试上传功能(upload_dir):
    """测试上传功能"""
    
    print(f"\n🧪 测试上传功能...")
    
    test_file = os.path.join(upload_dir, "a1.png")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    # 测试直接HTTP上传
    upload_url = "http://127.0.0.1:7860/gradio_api/upload"
    
    try:
        with open(test_file, 'rb') as f:
            files = {
                'files': (os.path.basename(test_file), f, 'image/png')
            }
            
            print(f"🚀 测试直接上传到: {upload_url}")
            response = requests.post(upload_url, files=files, timeout=30)
            
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 直接上传测试成功!")
                print(f"📋 上传结果: {result}")
                return True
            else:
                print(f"❌ 直接上传测试失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传测试错误: {e}")
        return False

def 生成新的MCP配置():
    """生成新的MCP配置"""
    
    print(f"\n📋 生成新的MCP配置...")
    
    config = {
        "mcpServers": {
            "gradio": {
                "url": "http://127.0.0.1:7860/gradio_api/mcp/sse"
            },
            "upload_files_to_gradio": {
                "command": "uvx",
                "args": [
                    "--from",
                    "gradio[mcp]",
                    "gradio",
                    "upload-mcp",
                    "http://127.0.0.1:7860",  # 使用基础URL
                    os.path.join(os.getcwd(), "mcp_uploads")
                ]
            }
        }
    }
    
    config_file = "mcp_config_fixed.json"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        import json
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 新配置已创建: {config_file}")
    print(f"🔧 关键修改: 使用基础URL http://127.0.0.1:7860")
    
    return config_file

def main():
    """主函数"""
    print("🔧 修复upload-mcp服务器URL问题")
    print("=" * 50)
    
    # 步骤1: 停止现有进程
    停止现有的upload_mcp进程()
    
    # 步骤2: 启动正确配置的服务器
    process, upload_dir = 启动正确的upload_mcp服务器()
    
    if process:
        try:
            # 步骤3: 测试上传功能
            if 测试上传功能(upload_dir):
                print("🎉 上传功能测试成功!")
            
            # 步骤4: 生成新配置
            config_file = 生成新的MCP配置()
            
            print(f"\n💡 使用说明:")
            print(f"1. 在MCP客户端中使用新配置: {config_file}")
            print(f"2. 重启MCP客户端以应用新配置")
            print(f"3. 现在应该可以正常上传文件了")
            
            print(f"\n⏳ 服务器将运行60秒后自动停止...")
            print(f"💡 如果测试成功，请手动重启MCP客户端")
            
            time.sleep(60)
            
        finally:
            print("🛑 正在停止upload-mcp服务器...")
            process.terminate()
            process.wait()
            print("✅ upload-mcp服务器已停止")
    else:
        print("❌ 无法启动upload-mcp服务器")
        
        # 提供备用解决方案
        print(f"\n🔧 备用解决方案:")
        print(f"手动启动upload-mcp服务器:")
        print(f"uvx --from gradio[mcp] gradio upload-mcp http://127.0.0.1:7860 {os.path.join(os.getcwd(), 'mcp_uploads')}")

if __name__ == "__main__":
    main()
