#!/usr/bin/env python3
"""
测试公网URL是否正常工作
"""

import requests
import json

def 测试公网URL():
    """测试公网URL是否可访问"""
    
    public_url = "https://fc56063ac9b9849b80.gradio.live"
    
    print(f"🔍 测试公网URL: {public_url}")
    
    try:
        # 测试主页
        response = requests.get(public_url, timeout=10)
        print(f"📡 主页响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 公网URL可访问")
            
            # 测试MCP schema
            schema_url = f"{public_url}/gradio_api/mcp/schema"
            schema_response = requests.get(schema_url, timeout=10)
            
            print(f"📡 MCP Schema响应状态码: {schema_response.status_code}")
            
            if schema_response.status_code == 200:
                tools = schema_response.json()
                print(f"✅ MCP Schema可访问")
                print(f"📋 可用工具数量: {len(tools)}")
                
                for tool in tools:
                    print(f"  🔧 {tool['name']}: {tool['description'][:50]}...")
                
                return True
            else:
                print(f"❌ MCP Schema不可访问")
                return False
        else:
            print(f"❌ 公网URL不可访问")
            return False
            
    except Exception as e:
        print(f"❌ 测试公网URL时出错: {e}")
        return False

def 生成最终配置():
    """生成最终的MCP配置"""
    
    print(f"\n📋 生成最终MCP配置...")
    
    config = {
        "mcpServers": {
            "gradio_ben2": {
                "url": "https://fc56063ac9b9849b80.gradio.live/gradio_api/mcp/sse"
            },
            "upload_files_to_gradio": {
                "command": "uvx",
                "args": [
                    "--from",
                    "gradio[mcp]",
                    "gradio",
                    "upload-mcp",
                    "https://fc56063ac9b9849b80.gradio.live",
                    "E:\\aibox\\ai\\BEN2\\BEN2\\mcp_uploads"
                ]
            }
        }
    }
    
    config_file = "mcp_config_final.json"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 最终配置已创建: {config_file}")
    print(f"📋 配置内容:")
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    return config_file

def 提供使用说明():
    """提供详细的使用说明"""
    
    print(f"\n📖 使用说明:")
    print(f"=" * 50)
    
    print(f"\n🎯 现在hostname验证问题已解决!")
    
    print(f"\n✅ 步骤1: 更新MCP客户端配置")
    print(f"使用新生成的配置文件: mcp_config_final.json")
    
    print(f"\n✅ 步骤2: 重启MCP客户端")
    print(f"重启您的MCP客户端以应用新配置")
    
    print(f"\n✅ 步骤3: 测试上传功能")
    print(f"在MCP工具中使用:")
    print(f'{{"file":"E:\\\\aibox\\\\ai\\\\BEN2\\\\BEN2\\\\mcp_uploads\\\\a1.png"}}')
    
    print(f"\n✅ 步骤4: 测试图像处理")
    print(f"上传成功后，使用返回的URL调用fn工具")
    
    print(f"\n💡 重要提示:")
    print(f"- 公网URL有效期1周")
    print(f"- 如果URL过期，重启app.py会生成新的URL")
    print(f"- 记得更新MCP配置中的URL")
    
    print(f"\n🔗 当前公网URL:")
    print(f"https://fc56063ac9b9849b80.gradio.live")

def main():
    """主函数"""
    print("🧪 测试公网URL和最终配置")
    print("=" * 40)
    
    # 测试公网URL
    if 测试公网URL():
        print(f"\n🎉 公网URL测试成功!")
        
        # 生成最终配置
        config_file = 生成最终配置()
        
        # 提供使用说明
        提供使用说明()
        
        print(f"\n🎯 问题已完全解决!")
        print(f"💡 现在可以正常使用MCP工具了!")
        
    else:
        print(f"\n❌ 公网URL测试失败")
        print(f"💡 请检查应用是否正常运行")

if __name__ == "__main__":
    main()
