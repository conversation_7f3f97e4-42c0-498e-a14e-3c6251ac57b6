#!/usr/bin/env python3
"""
测试MCP upload_files_to_gradio工具的中文测试用例
"""

import requests
import json
import os
import base64

def 获取MCP工具列表():
    """获取可用的MCP工具列表"""
    
    print("🔍 正在获取MCP工具列表...")
    
    try:
        schema_url = "http://127.0.0.1:7860/gradio_api/mcp/schema"
        response = requests.get(schema_url)
        
        if response.status_code == 200:
            tools = response.json()
            print(f"✅ 成功获取到 {len(tools)} 个MCP工具:")
            
            for tool in tools:
                print(f"  📋 工具名称: {tool['name']}")
                print(f"  📝 描述: {tool['description']}")
                print(f"  🔧 输入格式: {tool.get('inputSchema', {}).get('type', 'unknown')}")
                print()
            
            return tools
        else:
            print(f"❌ 获取MCP工具列表失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取MCP工具列表时出错: {e}")
        return None

def 检查upload_files_to_gradio工具():
    """检查是否存在upload_files_to_gradio工具"""
    
    print("🔍 正在检查upload_files_to_gradio工具...")
    
    tools = 获取MCP工具列表()
    if not tools:
        return False
    
    for tool in tools:
        if tool['name'] == 'upload_files_to_gradio':
            print(f"✅ 找到upload_files_to_gradio工具!")
            print(f"📝 描述: {tool['description']}")
            print(f"📋 输入格式: {json.dumps(tool['inputSchema'], indent=2, ensure_ascii=False)}")
            return True
    
    print("❌ 未找到upload_files_to_gradio工具")
    print("💡 可能需要使用标准的HTTP上传方式")
    return False

def 使用MCP_Streamable_HTTP调用工具(tool_name, arguments):
    """使用MCP Streamable HTTP协议调用工具"""
    
    print(f"🚀 正在通过MCP Streamable HTTP调用工具: {tool_name}")
    print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
    
    # MCP Streamable HTTP端点
    mcp_url = "http://127.0.0.1:7860/gradio_api/mcp/"
    
    # 构建MCP请求
    mcp_request = {
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        }
    }
    
    try:
        response = requests.post(mcp_url, json=mcp_request, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ MCP工具调用成功!")
            print(f"📋 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"❌ MCP工具调用失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ MCP工具调用时出错: {e}")
        return None

def 准备文件数据(file_path):
    """准备文件数据用于上传"""
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    print(f"📁 正在准备文件: {file_path}")
    print(f"📊 文件大小: {os.path.getsize(file_path)} 字节")
    
    try:
        # 读取文件并转换为base64
        with open(file_path, 'rb') as f:
            file_content = f.read()
            file_base64 = base64.b64encode(file_content).decode('utf-8')
        
        file_data = {
            "name": os.path.basename(file_path),
            "content": file_base64,
            "mime_type": "image/png"
        }
        
        print(f"✅ 文件数据准备完成")
        print(f"📋 文件名: {file_data['name']}")
        print(f"📋 MIME类型: {file_data['mime_type']}")
        print(f"📋 Base64长度: {len(file_base64)} 字符")
        
        return file_data
        
    except Exception as e:
        print(f"❌ 准备文件数据时出错: {e}")
        return None

def 测试MCP上传工具():
    """测试MCP upload_files_to_gradio工具"""
    
    print("🧪 开始测试MCP upload_files_to_gradio工具")
    print("=" * 60)
    
    file_path = r"E:\aibox\ai\BEN2\BEN2\upload\a1.png"
    
    # 步骤1: 检查工具是否存在
    if not 检查upload_files_to_gradio工具():
        print("⚠️ upload_files_to_gradio工具不可用，将使用标准HTTP上传")
        return 测试标准HTTP上传(file_path)
    
    # 步骤2: 准备文件数据
    file_data = 准备文件数据(file_path)
    if not file_data:
        return False
    
    # 步骤3: 调用MCP工具
    result = 使用MCP_Streamable_HTTP调用工具("upload_files_to_gradio", {
        "files": [file_data]
    })
    
    if result:
        print("🎉 MCP上传工具测试成功!")
        return result
    else:
        print("❌ MCP上传工具测试失败")
        return False

def 测试标准HTTP上传(file_path):
    """使用标准HTTP方式上传文件"""
    
    print("\n🔧 使用标准HTTP方式上传文件...")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    print(f"📁 正在上传: {file_path}")
    print(f"📊 文件大小: {os.path.getsize(file_path)} 字节")
    
    upload_url = "http://127.0.0.1:7860/gradio_api/upload"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'files': (os.path.basename(file_path), f, 'image/png')
            }
            
            print(f"🚀 正在上传到: {upload_url}")
            response = requests.post(upload_url, files=files)
            
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 标准HTTP上传成功!")
                print(f"📋 上传结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if isinstance(result, list) and len(result) > 0:
                    uploaded_path = result[0]
                    print(f"📂 上传后的服务器路径: {uploaded_path}")
                    return uploaded_path
                
                return result
            else:
                print(f"❌ 标准HTTP上传失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 标准HTTP上传时出错: {e}")
        return None

def 测试图像处理(uploaded_path):
    """测试图像处理功能"""
    
    print(f"\n🖼️ 开始测试图像处理功能...")
    print(f"📂 使用文件: {uploaded_path}")
    
    try:
        from gradio_client import Client
        
        # 创建客户端
        client = Client("http://127.0.0.1:7860")
        print(f"🔗 已连接到Gradio应用")
        
        # 创建正确的ImageData格式
        image_data = {
            "path": uploaded_path,
            "url": None,
            "size": os.path.getsize(uploaded_path) if os.path.exists(uploaded_path) else None,
            "orig_name": os.path.basename(uploaded_path),
            "mime_type": "image/png",
            "is_stream": False,
            "meta": {"_type": "gradio.FileData"}
        }
        
        print(f"📋 图像数据格式: {json.dumps(image_data, indent=2, ensure_ascii=False)}")
        
        # 调用图像处理功能
        print(f"🚀 正在处理图像...")
        result = client.predict(
            image=image_data,
            api_name="/image"
        )
        
        print(f"✅ 图像处理成功!")
        print(f"📋 处理结果类型: {type(result)}")
        print(f"📋 处理结果: {result}")
        
        # 分析结果
        if isinstance(result, (list, tuple)) and len(result) >= 2:
            result_image_path = result[0]
            result_file_path = result[1]
            
            print(f"🖼️ 结果图像路径: {result_image_path}")
            print(f"📁 结果文件路径: {result_file_path}")
            
            # 检查文件是否存在
            if isinstance(result_image_path, str) and os.path.exists(result_image_path):
                print(f"✅ 结果图像文件存在，大小: {os.path.getsize(result_image_path)} 字节")
            
            if isinstance(result_file_path, str) and os.path.exists(result_file_path):
                print(f"✅ 结果文件存在，大小: {os.path.getsize(result_file_path)} 字节")
            
            return True
        else:
            print(f"⚠️ 意外的结果格式: {result}")
            return False
        
    except Exception as e:
        print(f"❌ 图像处理时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 BEN2 MCP upload_files_to_gradio 中文测试")
    print("=" * 60)
    
    # 步骤1: 测试上传功能
    print("🔍 步骤1: 测试文件上传功能")
    uploaded_path = 测试MCP上传工具()
    
    if not uploaded_path:
        print("❌ 文件上传失败，测试终止")
        return
    
    # 步骤2: 测试图像处理
    print("\n🔍 步骤2: 测试图像处理功能")
    if 测试图像处理(uploaded_path):
        print("🎉 图像处理测试成功!")
    else:
        print("❌ 图像处理测试失败")
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成!")

if __name__ == "__main__":
    main()
