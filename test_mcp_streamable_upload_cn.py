#!/usr/bin/env python3
"""
测试MCP Streamable HTTP上传功能的中文测试用例
"""

import requests
import json
import os
import subprocess
import time
import threading

def 启动upload_mcp_服务器():
    """启动gradio upload-mcp服务器"""
    
    print("🚀 正在启动upload-mcp服务器...")
    
    # 创建上传目录
    upload_dir = os.path.join(os.getcwd(), "mcp_uploads")
    os.makedirs(upload_dir, exist_ok=True)
    print(f"📁 上传目录: {upload_dir}")
    
    # 构建命令
    cmd = [
        "uvx",
        "--from", "gradio[mcp]",
        "gradio",
        "upload-mcp",
        "http://127.0.0.1:7860/gradio_api/mcp/",
        upload_dir
    ]
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"✅ upload-mcp服务器已启动，PID: {process.pid}")
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ upload-mcp服务器运行正常")
            return process, upload_dir
        else:
            stdout, stderr = process.communicate()
            print(f"❌ upload-mcp服务器启动失败")
            print(f"📄 stdout: {stdout}")
            print(f"📄 stderr: {stderr}")
            return None, None
            
    except FileNotFoundError:
        print("❌ 未找到uvx命令，请先安装: pip install uv")
        return None, None
    except Exception as e:
        print(f"❌ 启动upload-mcp服务器时出错: {e}")
        return None, None

def 检查MCP服务器状态(url):
    """检查MCP服务器状态"""
    
    print(f"🔍 正在检查MCP服务器状态: {url}")
    
    try:
        # 尝试获取服务器信息
        response = requests.get(url, timeout=5)
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ MCP服务器运行正常")
            return True
        else:
            print(f"⚠️ MCP服务器响应异常: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到MCP服务器: {e}")
        return False

def 测试MCP_Streamable_HTTP上传(file_path, mcp_url):
    """使用MCP Streamable HTTP协议上传文件"""
    
    print(f"🚀 正在通过MCP Streamable HTTP上传文件...")
    print(f"📁 文件路径: {file_path}")
    print(f"🔗 MCP URL: {mcp_url}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    # 准备文件数据
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        print(f"📊 文件大小: {len(file_content)} 字节")
        
        # 构建MCP请求 - 使用tools/call方法
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": "upload_files_to_gradio",
                "arguments": {
                    "files": [{
                        "name": os.path.basename(file_path),
                        "content": file_content.hex(),  # 转换为hex字符串
                        "mime_type": "image/png"
                    }]
                }
            }
        }
        
        print(f"📋 MCP请求: {json.dumps({**mcp_request, 'params': {**mcp_request['params'], 'arguments': {'files': [{'name': '...', 'content': '...', 'mime_type': 'image/png'}]}}}, indent=2, ensure_ascii=False)}")
        
        # 发送请求
        response = requests.post(
            mcp_url,
            json=mcp_request,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout=30
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ MCP Streamable HTTP上传成功!")
            print(f"📋 响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"❌ MCP Streamable HTTP上传失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ MCP Streamable HTTP上传时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def 测试MCP工具列表(mcp_url):
    """获取MCP工具列表"""
    
    print(f"🔍 正在获取MCP工具列表: {mcp_url}")
    
    try:
        # 构建tools/list请求
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        response = requests.post(
            mcp_url,
            json=mcp_request,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功获取MCP工具列表!")
            print(f"📋 工具列表: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查是否有upload_files_to_gradio工具
            if 'result' in result and 'tools' in result['result']:
                tools = result['result']['tools']
                for tool in tools:
                    if tool.get('name') == 'upload_files_to_gradio':
                        print(f"✅ 找到upload_files_to_gradio工具!")
                        return True
                        
            print(f"❌ 未找到upload_files_to_gradio工具")
            return False
        else:
            print(f"❌ 获取MCP工具列表失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 获取MCP工具列表时出错: {e}")
        return False

def 测试不同的MCP端点():
    """测试不同的MCP端点"""
    
    print("🔍 正在测试不同的MCP端点...")
    
    endpoints = [
        "http://127.0.0.1:7860/gradio_api/mcp/",
        "http://127.0.0.1:7860/gradio_api/mcp",
        "http://127.0.0.1:7860/gradio_api/mcp/sse",
    ]
    
    for endpoint in endpoints:
        print(f"\n🔗 测试端点: {endpoint}")
        
        # 测试基本连接
        if 检查MCP服务器状态(endpoint):
            # 测试工具列表
            if 测试MCP工具列表(endpoint):
                print(f"✅ 端点 {endpoint} 支持upload_files_to_gradio")
                return endpoint
            else:
                print(f"⚠️ 端点 {endpoint} 不支持upload_files_to_gradio")
        else:
            print(f"❌ 端点 {endpoint} 无法连接")
    
    return None

def main():
    """主测试函数"""
    print("🧪 BEN2 MCP Streamable HTTP上传测试")
    print("=" * 60)
    
    file_path = r"E:\aibox\ai\BEN2\BEN2\upload\a1.png"
    
    # 步骤1: 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 测试文件不存在: {file_path}")
        return
    
    print(f"✅ 测试文件存在: {file_path}")
    print(f"📊 文件大小: {os.path.getsize(file_path)} 字节")
    
    # 步骤2: 测试现有的MCP端点
    print(f"\n🔍 步骤2: 测试现有的MCP端点")
    working_endpoint = 测试不同的MCP端点()
    
    if working_endpoint:
        print(f"\n🚀 步骤3: 使用工作的端点进行上传测试")
        result = 测试MCP_Streamable_HTTP上传(file_path, working_endpoint)
        
        if result:
            print("🎉 MCP Streamable HTTP上传测试成功!")
        else:
            print("❌ MCP Streamable HTTP上传测试失败")
    else:
        print(f"\n⚠️ 没有找到支持upload_files_to_gradio的MCP端点")
        print(f"💡 可能需要启动专门的upload-mcp服务器")
        
        # 步骤4: 尝试启动upload-mcp服务器
        print(f"\n🔍 步骤4: 尝试启动upload-mcp服务器")
        process, upload_dir = 启动upload_mcp_服务器()
        
        if process:
            try:
                print(f"✅ upload-mcp服务器已启动")
                print(f"💡 您现在可以在MCP客户端中使用以下配置:")
                print(json.dumps({
                    "mcpServers": {
                        "gradio": {
                            "url": "http://127.0.0.1:7860/gradio_api/mcp/"
                        },
                        "upload_files_to_gradio": {
                            "command": "uvx",
                            "args": [
                                "--from", "gradio[mcp]",
                                "gradio", "upload-mcp",
                                "http://127.0.0.1:7860/gradio_api/mcp/",
                                upload_dir
                            ]
                        }
                    }
                }, indent=2, ensure_ascii=False))
                
                # 等待用户测试
                input("\n按Enter键停止upload-mcp服务器...")
                
            finally:
                print("🛑 正在停止upload-mcp服务器...")
                process.terminate()
                process.wait()
                print("✅ upload-mcp服务器已停止")
    
    print("\n" + "=" * 60)
    print("✅ 测试完成!")

if __name__ == "__main__":
    main()
