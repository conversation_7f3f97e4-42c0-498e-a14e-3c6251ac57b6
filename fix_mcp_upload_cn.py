#!/usr/bin/env python3
"""
修复MCP上传问题的解决方案
"""

import os
import shutil
import json

def 复制文件到mcp目录():
    """将测试文件复制到MCP允许的目录"""
    
    source_file = r"E:\aibox\ai\BEN2\BEN2\upload\a1.png"
    target_dir = r"E:\aibox\ai\BEN2\BEN2\mcp_uploads"
    target_file = os.path.join(target_dir, "a1.png")
    
    print("🔧 解决MCP上传问题...")
    print(f"📁 源文件: {source_file}")
    print(f"📁 目标目录: {target_dir}")
    print(f"📁 目标文件: {target_file}")
    
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    
    # 检查源文件是否存在
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    try:
        # 复制文件
        shutil.copy2(source_file, target_file)
        print(f"✅ 文件复制成功!")
        print(f"📊 文件大小: {os.path.getsize(target_file)} 字节")
        
        return target_file
        
    except Exception as e:
        print(f"❌ 复制文件失败: {e}")
        return False

def 生成正确的MCP参数(file_path):
    """生成正确的MCP工具参数"""
    
    print(f"\n📋 生成MCP工具参数...")
    
    # 确保使用正确的路径格式
    normalized_path = os.path.normpath(file_path)
    
    mcp_params = {
        "file": normalized_path
    }
    
    print(f"✅ MCP参数生成完成:")
    print(json.dumps(mcp_params, indent=2, ensure_ascii=False))
    
    return mcp_params

def 创建更宽松的配置():
    """创建允许更多目录的MCP配置"""
    
    print(f"\n🔧 创建更宽松的MCP配置...")
    
    # 使用更高级别的目录作为允许的上传目录
    broader_upload_dir = r"E:\aibox\ai\BEN2\BEN2"
    
    config = {
        "mcpServers": {
            "gradio": {
                "url": "http://127.0.0.1:7860/gradio_api/mcp/sse"
            },
            "upload_files_to_gradio": {
                "command": "uvx",
                "args": [
                    "--from",
                    "gradio[mcp]",
                    "gradio",
                    "upload-mcp",
                    "http://127.0.0.1:7860/gradio_api/mcp/sse",
                    broader_upload_dir  # 使用更宽松的目录
                ]
            }
        }
    }
    
    config_file = "mcp_config_broader.json"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 更宽松的配置已创建: {config_file}")
    print(f"📋 新配置允许上传目录: {broader_upload_dir}")
    print(f"💡 这样就可以上传 upload/ 目录下的文件了!")
    
    return config_file

def 显示使用说明():
    """显示详细的使用说明"""
    
    print(f"\n📖 MCP上传工具使用说明:")
    print(f"=" * 50)
    
    print(f"\n🔍 问题原因:")
    print(f"  upload-mcp服务器有安全限制，只允许上传指定目录内的文件")
    
    print(f"\n✅ 解决方案1: 使用复制的文件")
    print(f"  在MCP工具中使用:")
    print(f'  {{"file":"E:\\\\aibox\\\\ai\\\\BEN2\\\\BEN2\\\\mcp_uploads\\\\a1.png"}}')
    
    print(f"\n✅ 解决方案2: 重启upload-mcp服务器")
    print(f"  1. 停止当前的upload-mcp服务器")
    print(f"  2. 使用新配置重启:")
    print(f"     uvx --from gradio[mcp] gradio upload-mcp \\")
    print(f"         http://127.0.0.1:7860/gradio_api/mcp/sse \\")
    print(f"         E:\\aibox\\ai\\BEN2\\BEN2")
    print(f"  3. 然后就可以上传 upload/ 目录下的文件了")
    
    print(f"\n🎯 推荐做法:")
    print(f"  使用解决方案1，因为它最简单且安全")

def main():
    """主函数"""
    print("🧪 修复MCP上传问题")
    print("=" * 40)
    
    # 步骤1: 复制文件到允许的目录
    target_file = 复制文件到mcp目录()
    
    if target_file:
        # 步骤2: 生成正确的MCP参数
        mcp_params = 生成正确的MCP参数(target_file)
        
        # 步骤3: 创建更宽松的配置（可选）
        config_file = 创建更宽松的配置()
        
        # 步骤4: 显示使用说明
        显示使用说明()
        
        print(f"\n🎉 问题已解决!")
        print(f"💡 现在您可以在MCP工具中使用复制的文件了!")
        
    else:
        print(f"❌ 无法解决问题，请检查文件路径")

if __name__ == "__main__":
    main()
