#!/usr/bin/env python3
"""
最终解决MCP hostname验证问题
"""

import subprocess
import time
import requests
import json
import os

def 重启应用获取公网URL():
    """重启应用并获取新的公网URL"""
    
    print("🔄 重启BEN2应用以获取公网URL...")
    
    # 停止现有应用进程
    try:
        print("🛑 正在停止现有应用...")
        subprocess.run(["taskkill", "/F", "/IM", "python.exe"], 
                      capture_output=True, check=False)
        time.sleep(2)
    except:
        pass
    
    # 启动新应用
    print("🚀 启动新应用...")
    process = subprocess.Popen(
        ["python", "app.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        cwd=os.getcwd()
    )
    
    # 等待应用启动并获取URL
    print("⏳ 等待应用启动...")
    public_url = None
    
    for i in range(30):  # 等待最多30秒
        try:
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                print(f"❌ 应用启动失败:")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return None, None
            
            # 尝试读取输出
            time.sleep(1)
            
            # 检查是否有gradio.live URL
            try:
                # 简单的方法：检查本地是否可访问
                response = requests.get("http://127.0.0.1:7860", timeout=2)
                if response.status_code == 200:
                    print("✅ 应用已启动，正在获取公网URL...")
                    
                    # 等待几秒让gradio生成公网URL
                    time.sleep(5)
                    
                    # 这里我们需要从应用输出中提取URL
                    # 由于无法直接读取，我们使用一个技巧
                    break
            except:
                continue
    
    return process, "需要手动获取"

def 提供手动解决方案():
    """提供手动解决方案"""
    
    print("\n📋 手动解决方案:")
    print("=" * 50)
    
    print("\n🎯 由于MCP客户端的安全限制，局域网IP无法通过验证")
    print("💡 必须使用公网HTTPS地址")
    
    print("\n✅ 解决步骤:")
    
    print("\n1️⃣ 重启BEN2应用")
    print("   - 应用已修改为 share=True")
    print("   - 重启后会显示公网URL")
    
    print("\n2️⃣ 获取公网URL")
    print("   - 查看应用启动日志")
    print("   - 找到类似这样的URL: https://xxx.gradio.live")
    
    print("\n3️⃣ 更新MCP配置")
    print("   - 将新的公网URL替换到MCP配置中")
    print("   - 重启MCP客户端")
    
    print("\n4️⃣ 测试功能")
    print("   - 上传文件应该正常")
    print("   - fn工具调用应该成功")

def 创建配置模板():
    """创建MCP配置模板"""
    
    print("\n📝 创建MCP配置模板...")
    
    config_template = {
        "mcpServers": {
            "gradio_ben2": {
                "url": "https://YOUR_GRADIO_URL.gradio.live/gradio_api/mcp/sse"
            },
            "upload_files_to_gradio": {
                "command": "uvx",
                "args": [
                    "--from",
                    "gradio[mcp]",
                    "gradio",
                    "upload-mcp",
                    "https://YOUR_GRADIO_URL.gradio.live",
                    "E:\\aibox\\ai\\BEN2\\BEN2\\mcp_uploads"
                ]
            }
        }
    }
    
    with open("mcp_config_template.json", 'w', encoding='utf-8') as f:
        json.dump(config_template, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置模板已创建: mcp_config_template.json")
    print("💡 请将 YOUR_GRADIO_URL 替换为实际的公网URL")

def 解释为什么必须用公网URL():
    """解释为什么必须使用公网URL"""
    
    print("\n🤔 为什么必须使用公网URL?")
    print("=" * 40)
    
    print("\n🔒 MCP客户端安全策略:")
    print("   ❌ 127.0.0.1 (本地回环) - 被禁止")
    print("   ❌ 192.168.x.x (私有网络) - 被禁止") 
    print("   ❌ 10.x.x.x (私有网络) - 被禁止")
    print("   ❌ HTTP协议 - 被禁止")
    print("   ✅ HTTPS公网地址 - 允许")
    
    print("\n💡 这是为了安全考虑:")
    print("   - 防止恶意应用访问本地服务")
    print("   - 确保通信加密")
    print("   - 验证服务器身份")
    
    print("\n🎯 Gradio Share的优势:")
    print("   ✅ 自动提供HTTPS")
    print("   ✅ 公网可访问")
    print("   ✅ 通过MCP验证")
    print("   ✅ 免费使用")
    print("   ⚠️ 有效期1周")

def main():
    """主函数"""
    print("🔧 最终解决MCP hostname验证问题")
    print("=" * 50)
    
    print("🔍 问题确认:")
    print("- upload_files_to_gradio 工具正常")
    print("- fn 工具调用时 hostname 验证失败")
    print("- 局域网IP (************) 被MCP客户端拒绝")
    
    # 解释原因
    解释为什么必须用公网URL()
    
    # 提供解决方案
    提供手动解决方案()
    
    # 创建配置模板
    创建配置模板()
    
    print("\n🎉 总结:")
    print("1. 应用已修改为 share=True")
    print("2. 重启应用获取新的公网URL")
    print("3. 更新MCP配置使用公网URL")
    print("4. 重启MCP客户端")
    print("5. 测试功能")
    
    print("\n💡 这是目前唯一可靠的解决方案!")

if __name__ == "__main__":
    main()
