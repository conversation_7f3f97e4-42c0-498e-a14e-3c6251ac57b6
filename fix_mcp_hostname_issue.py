#!/usr/bin/env python3
"""
修复MCP hostname验证失败问题
"""

import requests
import json
import os
import base64

def 测试不同的调用方式():
    """测试不同的MCP工具调用方式"""
    
    print("🔧 测试不同的MCP工具调用方式...")
    
    # 测试文件路径
    test_file = r"E:\aibox\ai\BEN2\BEN2\mcp_uploads\a1.png"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 测试文件: {test_file}")
    print(f"📊 文件大小: {os.path.getsize(test_file)} 字节")
    
    # 方案1: 使用base64编码的文件内容
    测试base64方案(test_file)
    
    # 方案2: 使用gradio_client直接调用
    测试gradio_client方案(test_file)
    
    # 方案3: 使用公网地址
    测试公网地址方案()

def 测试base64方案(file_path):
    """方案1: 使用base64编码的文件内容"""
    
    print(f"\n🔍 方案1: 使用base64编码")
    
    try:
        # 读取文件并转换为base64
        with open(file_path, 'rb') as f:
            file_content = f.read()
            file_base64 = base64.b64encode(file_content).decode('utf-8')
        
        # 创建data URL
        data_url = f"data:image/png;base64,{file_base64}"
        
        print(f"✅ Base64编码完成")
        print(f"📋 Data URL长度: {len(data_url)} 字符")
        print(f"💡 MCP工具参数:")
        
        mcp_params = {
            "image": data_url
        }
        
        print(json.dumps(mcp_params, indent=2)[:200] + "...")
        
        return data_url
        
    except Exception as e:
        print(f"❌ Base64方案失败: {e}")
        return None

def 测试gradio_client方案(file_path):
    """方案2: 使用gradio_client直接调用"""
    
    print(f"\n🔍 方案2: 使用gradio_client直接调用")
    
    try:
        from gradio_client import Client
        
        # 创建客户端
        client = Client("http://127.0.0.1:7860")
        print(f"🔗 已连接到Gradio应用")
        
        # 创建正确的ImageData格式
        image_data = {
            "path": file_path,
            "url": None,
            "size": os.path.getsize(file_path),
            "orig_name": os.path.basename(file_path),
            "mime_type": "image/png",
            "is_stream": False,
            "meta": {"_type": "gradio.FileData"}
        }
        
        print(f"🚀 正在处理图像...")
        result = client.predict(
            image=image_data,
            api_name="/image"
        )
        
        print(f"✅ gradio_client调用成功!")
        print(f"📋 结果: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ gradio_client方案失败: {e}")
        return None

def 测试公网地址方案():
    """方案3: 使用公网地址"""
    
    print(f"\n🔍 方案3: 使用公网地址")
    
    try:
        # 检查是否可以创建公网隧道
        print(f"💡 建议使用ngrok或gradio share功能:")
        print(f"1. 重启app.py时添加 share=True:")
        print(f"   demo.launch(show_error=True, inbrowser=True, mcp_server=True, share=True)")
        print(f"2. 或使用ngrok:")
        print(f"   ngrok http 7860")
        print(f"3. 然后使用公网URL替换127.0.0.1")
        
        return None
        
    except Exception as e:
        print(f"❌ 公网地址方案检查失败: {e}")
        return None

def 创建修复后的app配置():
    """创建修复后的app.py配置"""
    
    print(f"\n🔧 创建修复后的app.py配置...")
    
    # 读取当前app.py
    try:
        with open("app.py", 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # 检查是否已经有share=True
        if "share=True" in app_content:
            print(f"✅ app.py已经配置了share=True")
        else:
            print(f"💡 建议修改app.py，添加share=True:")
            print(f"将最后一行改为:")
            print(f"demo.launch(show_error=True, inbrowser=True, mcp_server=True, share=True)")
            
            # 创建修改建议
            修改建议 = """
# 在app.py的最后，将这行:
# demo.launch(show_error=True, inbrowser=True, mcp_server=True)

# 改为:
# demo.launch(show_error=True, inbrowser=True, mcp_server=True, share=True)

# 这样会创建一个公网可访问的URL，解决hostname验证问题
"""
            
            with open("app_modification_suggestion.txt", 'w', encoding='utf-8') as f:
                f.write(修改建议)
            
            print(f"✅ 修改建议已保存到: app_modification_suggestion.txt")
        
    except Exception as e:
        print(f"❌ 读取app.py失败: {e}")

def 提供完整解决方案():
    """提供完整的解决方案"""
    
    print(f"\n📋 完整解决方案:")
    print(f"=" * 50)
    
    print(f"\n🎯 推荐解决方案 (按优先级):")
    
    print(f"\n✅ 方案1: 使用Gradio Share功能")
    print(f"1. 修改app.py，在launch中添加 share=True")
    print(f"2. 重启应用，获得公网URL (如: https://xxx.gradio.live)")
    print(f"3. 更新MCP配置中的URL为公网地址")
    print(f"4. 重启MCP客户端")
    
    print(f"\n✅ 方案2: 使用Base64 Data URL")
    print(f"1. 不使用upload_file_to_gradio工具")
    print(f"2. 直接将图片转换为base64 data URL")
    print(f"3. 在fn工具中使用data URL作为image参数")
    
    print(f"\n✅ 方案3: 使用ngrok隧道")
    print(f"1. 安装ngrok: https://ngrok.com/")
    print(f"2. 运行: ngrok http 7860")
    print(f"3. 使用ngrok提供的公网URL")
    
    print(f"\n⚠️ 临时解决方案: 修改MCP客户端设置")
    print(f"某些MCP客户端可能允许配置信任的本地地址")

def main():
    """主函数"""
    print("🔧 修复MCP hostname验证失败问题")
    print("=" * 50)
    
    # 测试不同方案
    测试不同的调用方式()
    
    # 创建修复配置
    创建修复后的app配置()
    
    # 提供完整解决方案
    提供完整解决方案()
    
    print(f"\n🎉 分析完成!")
    print(f"💡 建议优先尝试方案1 (Gradio Share功能)")

if __name__ == "__main__":
    main()
