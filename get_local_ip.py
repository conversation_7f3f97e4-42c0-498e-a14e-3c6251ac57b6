#!/usr/bin/env python3
"""
获取本机真实IP地址
"""

import socket
import subprocess
import platform

def 获取本机IP地址():
    """获取本机在局域网中的真实IP地址"""
    
    print("🔍 正在获取本机IP地址...")
    
    try:
        # 方法1: 连接到外部地址获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            
        print(f"✅ 方法1 - 本机IP地址: {local_ip}")
        
        # 方法2: 使用hostname
        hostname = socket.gethostname()
        hostname_ip = socket.gethostbyname(hostname)
        
        print(f"✅ 方法2 - 主机名IP: {hostname_ip}")
        
        # 方法3: 使用系统命令
        if platform.system() == "Windows":
            result = subprocess.run(["ipconfig"], capture_output=True, text=True)
            print(f"💡 Windows网络配置:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'IPv4' in line and '192.168' in line:
                    ip = line.split(':')[-1].strip()
                    print(f"   📍 发现局域网IP: {ip}")
        
        return local_ip
        
    except Exception as e:
        print(f"❌ 获取IP地址失败: {e}")
        return None

def 生成IP配置():
    """生成使用真实IP的配置"""
    
    local_ip = 获取本机IP地址()
    
    if local_ip:
        print(f"\n🔧 生成使用真实IP的配置...")
        
        # 生成app.py修改建议
        app_config = f"""
# 修改app.py中的launch配置:
demo.launch(
    show_error=True, 
    inbrowser=True, 
    mcp_server=True, 
    share=False, 
    server_name="{local_ip}",  # 使用真实IP
    server_port=7860
)
"""
        
        print(f"📋 App.py配置建议:")
        print(app_config)
        
        # 生成MCP配置
        mcp_config = {
            "mcpServers": {
                "gradio_ben2": {
                    "url": f"http://{local_ip}:7860/gradio_api/mcp/sse"
                },
                "upload_files_to_gradio": {
                    "command": "uvx",
                    "args": [
                        "--from",
                        "gradio[mcp]",
                        "gradio",
                        "upload-mcp",
                        f"http://{local_ip}:7860",
                        "E:\\aibox\\ai\\BEN2\\BEN2\\mcp_uploads"
                    ]
                }
            }
        }
        
        import json
        with open("mcp_config_local_ip.json", 'w', encoding='utf-8') as f:
            json.dump(mcp_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ MCP配置已保存到: mcp_config_local_ip.json")
        print(f"📋 使用IP地址: {local_ip}")
        
        return local_ip
    
    return None

def 提供所有解决方案():
    """提供所有可能的解决方案"""
    
    print(f"\n📋 所有解决方案 (按推荐程度排序):")
    print(f"=" * 50)
    
    print(f"\n🥇 方案1: 使用真实局域网IP")
    local_ip = 生成IP配置()
    if local_ip:
        print(f"   ✅ 推荐使用IP: {local_ip}")
        print(f"   💡 修改app.py: server_name=\"{local_ip}\"")
    
    print(f"\n🥈 方案2: 使用Gradio Share (公网)")
    print(f"   ✅ 修改app.py: share=True")
    print(f"   💡 会生成类似: https://xxx.gradio.live")
    print(f"   ⚠️ 有效期1周，但最稳定")
    
    print(f"\n🥉 方案3: 使用ngrok隧道")
    print(f"   ✅ 安装ngrok: https://ngrok.com/")
    print(f"   💡 运行: ngrok http 7860")
    print(f"   ⚠️ 需要额外安装工具")
    
    print(f"\n🔧 方案4: 修改MCP客户端设置")
    print(f"   ✅ 某些客户端允许信任本地地址")
    print(f"   💡 查看客户端的安全设置")
    print(f"   ⚠️ 不是所有客户端都支持")

def main():
    """主函数"""
    print("🔧 解决hostname验证失败问题")
    print("=" * 40)
    
    print(f"🔍 问题分析:")
    print(f"- MCP客户端不信任 0.0.0.0 地址")
    print(f"- 需要使用真实的、可验证的IP地址")
    print(f"- 或使用公网地址绕过验证")
    
    提供所有解决方案()
    
    print(f"\n💡 推荐做法:")
    print(f"1. 优先尝试方案1 (真实IP)")
    print(f"2. 如果不行，使用方案2 (share=True)")
    print(f"3. 方案2最稳定，但需要网络连接")

if __name__ == "__main__":
    main()
