#!/usr/bin/env python3
"""
Test script to verify MCP server functionality for BEN2 application.
"""

import requests
import json

def test_mcp_schema():
    """Test if MCP schema endpoint is accessible and returns expected tools."""
    try:
        response = requests.get("http://127.0.0.1:7860/gradio_api/mcp/schema")
        if response.status_code == 200:
            schema = response.json()
            print("✅ MCP Schema endpoint is accessible")
            print(f"📊 Found {len(schema)} tools:")
            
            for tool in schema:
                print(f"  - {tool['name']}: {tool['description'][:100]}...")
            
            # Check if expected tools are present
            tool_names = [tool['name'] for tool in schema]
            expected_tools = ['fn', 'process_video']
            
            for expected_tool in expected_tools:
                if expected_tool in tool_names:
                    print(f"✅ Tool '{expected_tool}' found")
                else:
                    print(f"❌ Tool '{expected_tool}' missing")
            
            return True
        else:
            print(f"❌ MCP Schema endpoint returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing MCP schema: {e}")
        return False

def test_gradio_api():
    """Test if Gradio API endpoint is accessible."""
    try:
        response = requests.get("http://127.0.0.1:7860/gradio_api/")
        if response.status_code == 200:
            print("✅ Gradio API endpoint is accessible")
            return True
        else:
            print(f"❌ Gradio API endpoint returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing Gradio API: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing BEN2 MCP Server Integration")
    print("=" * 50)
    
    # Test Gradio API
    gradio_ok = test_gradio_api()
    
    # Test MCP Schema
    mcp_ok = test_mcp_schema()
    
    print("\n" + "=" * 50)
    if gradio_ok and mcp_ok:
        print("🎉 All tests passed! MCP server is working correctly.")
        print("\n📋 MCP Client Configuration:")
        print("For SSE-compatible clients (Cursor, Windsurf, Cline):")
        print(json.dumps({
            "mcpServers": {
                "ben2": {
                    "url": "http://127.0.0.1:7860/gradio_api/mcp/sse"
                }
            }
        }, indent=2))
        
        print("\nFor Claude Desktop (requires mcp-remote):")
        print(json.dumps({
            "mcpServers": {
                "ben2": {
                    "command": "npx",
                    "args": [
                        "mcp-remote",
                        "http://127.0.0.1:7860/gradio_api/mcp/sse"
                    ]
                }
            }
        }, indent=2))
    else:
        print("❌ Some tests failed. Please check the application.")

if __name__ == "__main__":
    main()
